<?php

declare(strict_types=1);

namespace Comave\Customer\Plugin;

use Magento\Customer\Controller\Account\LoginPost;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Controller\ResultInterface;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;
use Magento\Customer\Model\Session;

class RedirectSellerLogin
{
    private const SELLER_ACCOUNT_LOGIN = 'marketplace/account/login';

    public function __construct(
        private readonly RedirectFactory $resultRedirectFactory,
        private readonly RequestInterface $request,
        private readonly MarketplaceHelper $mpHelperData,
        private readonly Session $session
    ) {}

    /**
     * @param Login $subject
     * @param ResultInterface $result
     * @return ResultInterface
     */
    public function afterExecute(LoginPost $subject, ResultInterface $result): ResultInterface
    {
        $customerId = $this->session->getCustomerId();
        if (!$customerId) {
            $referer = $this->request->getServer('HTTP_REFERER');
            if ($referer && strpos($referer, '/marketplace/account/login') !== false) {
                $resultRedirect = $this->resultRedirectFactory->create();
                return $resultRedirect->setPath(self::SELLER_ACCOUNT_LOGIN);
            }
            return $result;
        }

        $isSeller = $this->isSeller((int)$customerId);
        $this->session->setData('is_seller', $isSeller);

        $resultRedirect = $this->resultRedirectFactory->create();

        return $resultRedirect->setPath(self::SELLER_ACCOUNT_LOGIN);
    }

    /**
     * Check is seller
     * @param int $customerId
     * @return bool
     */
    private function isSeller(int $customerId): bool
    {
        return $this->mpHelperData->getSellerCollectionObj($customerId)
                ->addFieldToFilter('is_seller', true)
                ->getSize() > 0;
    }
}
