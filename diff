diff --git a/app/code/Comave/Customer/Plugin/RedirectSellerLogin.php b/app/code/Comave/Customer/Plugin/RedirectSellerLogin.php
index ea446a502..1595947e1 100644
--- a/app/code/Comave/Customer/Plugin/RedirectSellerLogin.php
+++ b/app/code/Comave/Customer/Plugin/RedirectSellerLogin.php
@@ -31,6 +31,11 @@ class RedirectSellerLogin
     {
         $customerId = $this->session->getCustomerId();
         if (!$customerId) {
+            $referer = $this->request->getServer('HTTP_REFERER');
+            if ($referer && strpos($referer, '/marketplace/account/login') !== false) {
+                $resultRedirect = $this->resultRedirectFactory->create();
+                return $resultRedirect->setPath(self::SELLER_ACCOUNT_LOGIN);
+            }
             return $result;
         }
 
@@ -38,11 +43,8 @@ class RedirectSellerLogin
         $this->session->setData('is_seller', $isSeller);
 
         $resultRedirect = $this->resultRedirectFactory->create();
-        $redirectPath = ($isSeller) ?
-            self::SELLER_ACCOUNT_LOGIN
-            : \Magento\Customer\Model\Url::ROUTE_ACCOUNT_LOGIN;
 
-        return $resultRedirect->setPath($redirectPath);
+        return $resultRedirect->setPath(self::SELLER_ACCOUNT_LOGIN);
     }
 
     /**
diff --git a/app/code/Comave/Customer/Plugin/RedirectSellerLogout.php b/app/code/Comave/Customer/Plugin/RedirectSellerLogout.php
new file mode 100644
index *********..09985baf3
--- /dev/null
+++ b/app/code/Comave/Customer/Plugin/RedirectSellerLogout.php
@@ -0,0 +1,42 @@
+<?php
+
+declare(strict_types=1);
+
+namespace Comave\Customer\Plugin;
+
+use Magento\Customer\Controller\Account\LogoutSuccess;
+use Magento\Framework\App\RequestInterface;
+use Magento\Framework\Controller\Result\RedirectFactory;
+use Magento\Framework\Controller\ResultInterface;
+use Magento\Framework\Message\ManagerInterface;
+
+class RedirectSellerLogout
+{
+    private const SELLER_ACCOUNT_LOGIN = 'marketplace/account/login';
+
+    public function __construct(
+        private readonly RedirectFactory $resultRedirectFactory,
+        private readonly RequestInterface $request,
+        private readonly ManagerInterface $messageManager
+    ) {}
+
+    /**
+     * Handle logout redirection for marketplace users
+     *
+     * @param LogoutSuccess $subject
+     * @param \Closure $proceed
+     * @return ResultInterface
+     */
+    public function aroundExecute(LogoutSuccess $subject, \Closure $proceed): ResultInterface
+    {
+        $referer = $this->request->getServer('HTTP_REFERER');
+        
+        if ($referer && strpos($referer, '/marketplace/') !== false) {
+            $this->messageManager->addSuccessMessage(__('You have been logged out successfully.'));
+            $resultRedirect = $this->resultRedirectFactory->create();
+            $resultRedirect->setPath(self::SELLER_ACCOUNT_LOGIN);
+            return $resultRedirect;
+        }
+        return $proceed();
+    }
+}
diff --git a/app/code/Comave/Customer/etc/frontend/di.xml b/app/code/Comave/Customer/etc/frontend/di.xml
index d2ce8f2ba..981b79298 100644
--- a/app/code/Comave/Customer/etc/frontend/di.xml
+++ b/app/code/Comave/Customer/etc/frontend/di.xml
@@ -6,4 +6,9 @@
                 type="Comave\Customer\Plugin\RedirectSellerLogin"
                 sortOrder="1"/>
     </type>
+    <type name="Magento\Customer\Controller\Account\LogoutSuccess">
+        <plugin name="redirect_seller_logout"
+                type="Comave\Customer\Plugin\RedirectSellerLogout"
+                sortOrder="1"/>
+    </type>
 </config>
